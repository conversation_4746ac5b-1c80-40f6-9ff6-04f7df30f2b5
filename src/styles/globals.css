@import "tailwindcss";
@import "tw-animate-css";
@import "uploadthing/tw/v4";
@source "../../node_modules/@uploadthing/react/dist";

@custom-variant dark (&:is(.dark *));

@custom-variant dark (&:is(.dark *));

@theme {
	--color-border: hsl(var(--border));
	--color-input: hsl(var(--input));
	--color-ring: hsl(var(--ring));
	--color-background: hsl(var(--background));
	--color-foreground: hsl(var(--foreground));

	--color-primary: hsl(var(--primary));
	--color-primary-foreground: hsl(var(--primary-foreground));

	--color-secondary: hsl(var(--secondary));
	--color-secondary-foreground: hsl(var(--secondary-foreground));

	--color-destructive: hsl(var(--destructive));
	--color-destructive-foreground: hsl(var(--destructive-foreground));

	--color-muted: hsl(var(--muted));
	--color-muted-foreground: hsl(var(--muted-foreground));

	--color-accent: hsl(var(--accent));
	--color-accent-foreground: hsl(var(--accent-foreground));

	--color-popover: hsl(var(--popover));
	--color-popover-foreground: hsl(var(--popover-foreground));

	--color-card: hsl(var(--card));
	--color-card-foreground: hsl(var(--card-foreground));

	--radius-lg: var(--radius);
	--radius-md: calc(var(--radius) - 2px);
	--radius-sm: calc(var(--radius) - 4px);

	--font-sans: var(--font-sans), ui-sans-serif, system-ui, sans-serif,
		"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

	--animate-accordion-down: accordion-down 0.2s ease-out;
	--animate-accordion-up: accordion-up 0.2s ease-out;

	@keyframes accordion-down {
		from {
			height: 0;
		}
		to {
			height: var(--radix-accordion-content-height);
		}
	}
	@keyframes accordion-up {
		from {
			height: var(--radix-accordion-content-height);
		}
		to {
			height: 0;
		}
	}
}

@utility container {
	margin-inline: auto;
	padding-inline: 2rem;
	@media (width >= 640px) {
		max-width: none;
	}
	@media (width >= 1400px) {
		max-width: 1400px;
	}
}

@theme inline {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}
}

@utility debug {
	@apply border border-red-500;
}

@utility horizontal {
	@apply flex flex-row;
}

@utility h {
	@apply flex flex-row;
}

@utility v {
	@apply flex flex-col;
}

@utility vertical {
	@apply flex flex-col;
}

:root {
	--radius: 0.5rem;
	--header-height: 60px; /* Matching the HEADER_HEIGHT from config */
	--container-max-width: 1200px; /* Standard max width for containers across the app */
	--background: oklch(1 0 0);
	--foreground: oklch(0.141 0.005 285.823);
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.141 0.005 285.823);
	--popover: oklch(1 0 0);
	--popover-foreground: oklch(0.141 0.005 285.823);
	--primary: #5e9943;
	--primary-foreground: oklch(0.969 0.016 293.756);
	--secondary: oklch(0.967 0.001 286.375);
	--secondary-foreground: oklch(0.21 0.006 285.885);
	--muted: oklch(0.967 0.001 286.375);
	--muted-foreground: oklch(0.552 0.016 285.938);
	--accent: oklch(0.967 0.001 286.375);
	--accent-foreground: oklch(0.21 0.006 285.885);
	--destructive: oklch(0.577 0.245 27.325);
	--border: oklch(0.92 0.004 286.32);
	--input: oklch(0.92 0.004 286.32);
	--ring: #5e9943;
	--chart-1: oklch(0.646 0.222 41.116);
	--chart-2: oklch(0.6 0.118 184.704);
	--chart-3: oklch(0.398 0.07 227.392);
	--chart-4: oklch(0.828 0.189 84.429);
	--chart-5: oklch(0.769 0.188 70.08);
	--sidebar: oklch(0.985 0 0);
	--sidebar-foreground: oklch(0.141 0.005 285.823);
	--sidebar-primary: oklch(0.606 0.25 292.717);
	--sidebar-primary-foreground: oklch(0.969 0.016 293.756);
	--sidebar-accent: oklch(0.967 0.001 286.375);
	--sidebar-accent-foreground: oklch(0.21 0.006 285.885);
	--sidebar-border: oklch(0.92 0.004 286.32);
	--sidebar-ring: oklch(0.606 0.25 292.717);
	--color-zinc: oklch(70.5% 0.015 286.067);
}

.dark {
	--background: oklch(0.141 0.005 285.823);
	--foreground: oklch(0.985 0 0);
	--card: oklch(0.21 0.006 285.885);
	--card-foreground: oklch(0.985 0 0);
	--popover: oklch(0.21 0.006 285.885);
	--popover-foreground: oklch(0.985 0 0);
	--primary: oklch(0.541 0.281 293.009);
	--primary-foreground: oklch(0.969 0.016 293.756);
	--secondary: oklch(0.274 0.006 286.033);
	--secondary-foreground: oklch(0.985 0 0);
	--muted: oklch(0.274 0.006 286.033);
	--muted-foreground: oklch(0.705 0.015 286.067);
	--accent: oklch(0.274 0.006 286.033);
	--accent-foreground: oklch(0.985 0 0);
	--destructive: oklch(0.704 0.191 22.216);
	--border: oklch(1 0 0 / 10%);
	--input: oklch(1 0 0 / 15%);
	--ring: oklch(0.541 0.281 293.009);
	--chart-1: oklch(0.488 0.243 264.376);
	--chart-2: oklch(0.696 0.17 162.48);
	--chart-3: oklch(0.769 0.188 70.08);
	--chart-4: oklch(0.627 0.265 303.9);
	--chart-5: oklch(0.645 0.246 16.439);
	--sidebar: oklch(0.21 0.006 285.885);
	--sidebar-foreground: oklch(0.985 0 0);
	--sidebar-primary: oklch(0.541 0.281 293.009);
	--sidebar-primary-foreground: oklch(0.969 0.016 293.756);
	--sidebar-accent: oklch(0.274 0.006 286.033);
	--sidebar-accent-foreground: oklch(0.985 0 0);
	--sidebar-border: oklch(1 0 0 / 10%);
	--sidebar-ring: oklch(0.541 0.281 293.009);
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}
	body {
		@apply from-muted/90 to-background text-foreground bg-linear-to-b;
	}

	:root {
		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
	}

	.dark {
		--chart-1: 220 70% 50%;
		--chart-2: 160 60% 45%;
		--chart-3: 30 80% 55%;
		--chart-4: 280 65% 60%;
		--chart-5: 340 75% 55%;
	}
}
.vertical {
	@apply flex flex-col;
}

.horizontal {
	@apply flex flex-row;
}

.vertical.center {
	@apply items-center justify-center;
}

.horizontal.center {
	@apply items-center justify-center;
}

.horizontal.center-v {
	@apply items-center;
}

.vertical.center-h {
	@apply justify-center;
}

/*
  ---break---
*/

@layer base {
	* {
		@apply border-border outline-ring/50;
	}
	body {
		@apply bg-background text-foreground;
	}
}

@keyframes slide-out {
	from {
		transform: translateX(0);
		opacity: 1;
	}
	to {
		transform: translateX(-100px);
		opacity: 0;
	}
}

@keyframes slide-in {
	from {
		transform: translateX(100px);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}

@keyframes pulse-subtle {
	0%,
	100% {
		opacity: 1;
	}
	50% {
		opacity: 0.8;
	}
}

::view-transition-old(page) {
	animation: slide-out 0.3s ease-in-out;
}

::view-transition-new(page) {
	animation: slide-in 0.3s ease-in-out;
}

.animate-pulse-subtle {
	animation: pulse-subtle 2s ease-in-out infinite;
}
