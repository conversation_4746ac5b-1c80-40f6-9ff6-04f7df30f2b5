# You can use a UI to generate your full .env file on https://env.zerotoshipped.com

# 🔌 Flags → Integrations
NEXT_PUBLIC_ENABLE_POLAR=false
NEXT_PUBLIC_ENABLE_BACKGROUND_JOBS=false
NEXT_PUBLIC_ENABLE_GITHUB_INTEGRATION=false
NEXT_PUBLIC_ENABLE_UPLOADTHING=false
NEXT_PUBLIC_ENABLE_CRON=false

# 🚩 Flags → Pages
NEXT_PUBLIC_ENABLE_BLOG_PAGE=false
NEXT_PUBLIC_ENABLE_ABOUT_PAGE=false
NEXT_PUBLIC_ENABLE_PRICING_PAGE=false
NEXT_PUBLIC_ENABLE_CHAT_PAGE=false

# 🔧 General
NEXT_PUBLIC_APP_NAME=ZTS
NEXT_PUBLIC_APP_DESCRIPTION="Zero To Shipped Demo"

# ⚙️ Settings
DATABASE_URL="postgresql://postgres@localhost:5432/your-local-db-name"

# 🔑 Better auth
BETTER_AUTH_SECRET="f9150624-f48d-4b25-8cbe-5dd8609c2b2a"
NEXT_PUBLIC_AUTH_ENABLE_EMAIL_VERIFICATION=false

# Email
NEXT_PUBLIC_EMAIL_PROVIDER=none
EMAIL_FROM="<EMAIL>"
NEXT_PUBLIC_EMAIL_ENABLE_EMAIL_PREVIEW=true
NEXT_PUBLIC_EMAIL_PREVIEW_OPEN_TAB=true

#Microservice
NEXT_PUBLIC_SCHEDULER_API_URL=http://localhost:5007
NEXT_PUBLIC_SCHEDULER_API_KEY=fake