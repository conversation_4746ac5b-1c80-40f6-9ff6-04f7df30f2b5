-- PostgreSQL Migration Script to transfer data from old schema to new schema
-- This script assumes both schemas exist in the same database but with different tables

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- Create temporary table to map old user IDs to new user IDs
CREATE TEMPORARY TABLE user_id_mapping (
    old_id INT PRIMARY KEY,
    new_id VARCHAR NOT NULL
);

-- Step 1: Migrate Users
-- First, insert users from the old schema into the new schema
INSERT INTO "users" (
    id,
    createdAt,
    updatedAt,
    name,
    email,
    emailVerified,
    timezone,
    onboarded,
    role
)
SELECT
    gen_random_uuid()::text, -- Generate UUID for string ID
    u.createdAt,
    u.updatedAt,
    concat(u.firstName, ' ', u.lastName), -- Combine first and last name
    u.email,
    TRUE, -- Default to email verified
    'Etc/GMT', -- Default timezone
    TRUE, -- Default to onboarded
    CASE 
        WHEN u.role = 'USER' THEN 'user'
        ELSE lower(u.role)
    END -- Convert role format
FROM "User" u
ON CONFLICT (email) DO UPDATE SET
    -- Only update these fields if the user already exists
    name = EXCLUDED.name,
    updatedAt = EXCLUDED.updatedAt,
    role = EXCLUDED.role;

-- Store the mapping between old and new user IDs
INSERT INTO user_id_mapping (old_id, new_id)
SELECT u.id, nu.id
FROM "User" u
JOIN "users" nu ON u.email = nu.email;

-- Step 2: Create accounts with default password
-- Using argon2 for password hashing (adjust based on your auth system)
-- Password: 'password'
INSERT INTO "accounts" (
    id,
    accountId,
    providerId,
    userId,
    password,
    createdAt,
    updatedAt
)
SELECT
    gen_random_uuid()::text, -- Generate UUID for account ID
    'local', -- Local account
    'email', -- Email provider
    m.new_id, -- New user ID
    '$argon2id$v=19$m=65536,t=3,p=4$YOuOQW+N8b43xhhDSCmFMA$3Pnn7+7rwnRVpA6C1GOS1HdL/Gd9EHsw/FcvGr+H/lE', -- Hashed 'password'
    NOW(), -- Created now
    NOW() -- Updated now
FROM user_id_mapping m
ON CONFLICT DO NOTHING;

-- Step 3: Migrate GroceryTrips
-- Create new GroceryTrips linked to new user IDs
INSERT INTO "GroceryTrip" (
    id,
    createdAt,
    updatedAt,
    name,
    description,
    userId
)
SELECT
    gt.id,
    gt.createdAt,
    gt.updatedAt,
    gt.name,
    gt.description,
    m.new_id -- Map to new user ID
FROM "GroceryTrip" gt
JOIN user_id_mapping m ON gt.userId = m.old_id
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updatedAt = EXCLUDED.updatedAt,
    userId = EXCLUDED.userId;

-- Step 4: Migrate Receipts
-- Receipts should maintain their relationship with GroceryTrip
INSERT INTO "Receipt" (
    id,
    url,
    createdAt,
    updatedAt,
    groceryTripId,
    status,
    scrapedData
)
SELECT
    r.id,
    r.url,
    r.createdAt,
    r.updatedAt,
    r.groceryTripId,
    r.status,
    r.scrapedData
FROM "Receipt" r
ON CONFLICT (id) DO UPDATE SET
    url = EXCLUDED.url,
    updatedAt = EXCLUDED.updatedAt,
    groceryTripId = EXCLUDED.groceryTripId,
    status = EXCLUDED.status,
    scrapedData = EXCLUDED.scrapedData;

-- Step 5: Migrate Items
-- Items need to reference both the new user ID and maintain GroceryTrip relationship
INSERT INTO "Item" (
    id,
    name,
    description,
    price,
    quantity,
    importId,
    unit,
    createdAt,
    updatedAt,
    status,
    percentConsumed,
    userId,
    groceryTripId,
    receiptId
)
SELECT
    i.id,
    i.name,
    i.description,
    i.price,
    i.quantity,
    i.importId,
    i.unit,
    i.createdAt,
    i.updatedAt,
    i.status,
    i.percentConsumed,
    m.new_id, -- Map to new user ID
    i.groceryTripId,
    i.receiptId
FROM "Item" i
JOIN user_id_mapping m ON i.userId = m.old_id
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    price = EXCLUDED.price,
    quantity = EXCLUDED.quantity,
    unit = EXCLUDED.unit,
    updatedAt = EXCLUDED.updatedAt,
    status = EXCLUDED.status,
    percentConsumed = EXCLUDED.percentConsumed,
    userId = EXCLUDED.userId,
    groceryTripId = EXCLUDED.groceryTripId,
    receiptId = EXCLUDED.receiptId;

-- Step 6: Migrate ItemType relationships
-- Maintain many-to-many relationships between Items and ItemTypes
INSERT INTO "_ItemToItemType" (
    "A", -- Item ID
    "B"  -- ItemType ID
)
SELECT
    i2it."A",
    i2it."B"
FROM "_ItemToItemType" i2it
ON CONFLICT DO NOTHING;

-- Step 7: Migrate Reminders
-- Update reminders to reference new user IDs
INSERT INTO "Reminder" (
    id,
    createdAt,
    updatedAt,
    time,
    itemId,
    userId
)
SELECT
    r.id,
    r.createdAt,
    r.updatedAt,
    r.time,
    r.itemId,
    m.new_id -- Map to new user ID
FROM "Reminder" r
JOIN user_id_mapping m ON r.userId = m.old_id
ON CONFLICT (id) DO UPDATE SET
    time = EXCLUDED.time,
    updatedAt = EXCLUDED.updatedAt,
    userId = EXCLUDED.userId;

-- Clean up temporary table
DROP TABLE user_id_mapping;

-- Commit the transaction
COMMIT;