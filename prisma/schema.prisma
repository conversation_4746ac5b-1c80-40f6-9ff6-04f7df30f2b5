generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
  previewFeatures = ["fullTextSearchPostgres"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  // directUrl = env("DIRECT_URL") //uncomment for supabase
}

model User {
  id            String    @id
  createdAt     DateTime
  updatedAt     DateTime? @updatedAt
  name          String
  email         String    @unique
  emailVerified Boolean
  image         String?
  bio           String?

  // images → direct URL uploads
  avatarImageUrl String?
  coverImageUrl  String?

  // images → UploadThing keys
  avatarImageUTKey String?
  coverImageUTKey  String?

  // images → DB relations
  avatarImageId  String?   @unique
  avatarImage    UTImage?  @relation("AvatarImage", fields: [avatarImageId], references: [id], onDelete: SetNull)
  coverImageId   String?   @unique
  coverImage     UTImage?  @relation("CoverImage", fields: [coverImageId], references: [id], onDelete: SetNull)
  uploadedImages UTImage[] @relation("UploadedImages") // Relation for all uploaded images by user

  timezone String? @default("Etc/GMT")

  preferences Json? @default("{}")

  sessions Session[]
  accounts Account[]
  groceryTrips   GroceryTrip[]
  items          Item[]
  reminders      Reminder[]

  // better-auth-admin
  role       String    @default("user")
  banReason  String?
  banExpires DateTime?
  banned     Boolean   @default(false)

  // better-auth-username
  username        String?
  displayUsername String?

  onboarded Boolean @default(false)

  @@unique([username])
  @@map("users")
}

model Session {
  id             String    @id
  expiresAt      DateTime
  token          String    @unique
  createdAt      DateTime
  updatedAt      DateTime? @updatedAt
  ipAddress      String?
  userAgent      String?
  userId         String
  user           User      @relation(fields: [userId], references: [id])
  impersonatedBy String?

  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id])
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime? @updatedAt

  @@map("accounts")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime? @updatedAt

  @@map("verifications")
}

model UTImage {
  id        String   @id @default(cuid())
  key       String   @unique // The key returned by UploadThing
  userId    String
  user      User     @relation("UploadedImages", fields: [userId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  usedAsAvatarByUser User? @relation("AvatarImage")
  usedAsCoverByUser  User? @relation("CoverImage")

  @@index([userId])
  @@map("ut_images")
}

model Item {
  id              Int            @id @default(autoincrement())
  name            String
  description     String         @default("")
  price           Float
  quantity        Int
  importId        String         @default("")
  unit            String         @default("")
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  status          ItemStatusType @default(FRESH)
  percentConsumed Int            @default(0)
  userId          String
  groceryTripId   Int
  groceryTrip     GroceryTrip    @relation(fields: [groceryTripId], references: [id], onDelete: Cascade)
  receiptId       Int?
  receipt         Receipt?       @relation(fields: [receiptId], references: [id], onDelete: Cascade)
  user            User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  reminders       Reminder[]
  itemTypes       ItemType[]
}

model ItemType {
  id                          Int      @id @default(autoincrement())
  createdAt                   DateTime @default(now())
  updatedAt                   DateTime @updatedAt
  name                        String   @unique
  category                    String
  storage_advice              String
  suggested_life_span_seconds Int
  items                       Item[]
}

model GroceryTrip {
  id          Int       @id @default(autoincrement())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  name        String
  description String    @default("")
  userId      String
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  items       Item[]
  receipts    Receipt[]
}

model Receipt {
  id            Int           @id @default(autoincrement())
  url           String
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  groceryTripId Int?
  GroceryTrip   GroceryTrip?  @relation(fields: [groceryTripId], references: [id], onDelete: Cascade)
  items         Item[]
  status        ReceiptStatus @default(PROCESSING)
  scrapedData   Json?
}

model Reminder {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  time      DateTime
  itemId    Int
  userId    String
  item      Item     @relation(fields: [itemId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

enum TokenType {
  RESET_PASSWORD
}

enum ItemStatusType {
  BAD
  OLD
  FRESH
  EATEN
  DISCARDED
}

enum ReceiptStatus {
  PROCESSING
  IMPORTED
  ERROR
}
